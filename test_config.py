#!/usr/bin/env python3
"""Simple test script to validate the worker configuration."""

import yaml
import sys

def test_config():
    try:
        print("Testing YAML syntax...")
        with open('iscan_config.yaml', 'r') as f:
            config = yaml.safe_load(f)
        
        print("✅ YAML syntax is valid!")
        
        # Test worker configuration
        trufflehog_config = config.get('trufflehog', {})
        
        restore_workers = trufflehog_config.get('restore_workers', 'NOT SET')
        extract_workers = trufflehog_config.get('extract_workers', 'NOT SET')
        unpack_workers = trufflehog_config.get('unpack_workers', 'NOT SET')
        max_direct_extracts = trufflehog_config.get('max_direct_extracts', 'NOT SET')
        
        print(f"restore_workers: {restore_workers}")
        print(f"extract_workers: {extract_workers}")
        print(f"unpack_workers: {unpack_workers}")
        print(f"max_direct_extracts: {max_direct_extracts}")
        
        # Validate values
        if restore_workers == 8 and extract_workers == 4 and unpack_workers == 4:
            print("✅ Worker configuration is correctly set!")
            return True
        else:
            print("❌ Worker configuration values are not as expected")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    success = test_config()
    sys.exit(0 if success else 1)
